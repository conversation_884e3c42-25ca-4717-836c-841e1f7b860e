# DuckDB Rust API Reference for Complex Types

## Overview

This document catalogs the DuckDB Rust API patterns for handling complex nested types, specifically focusing on STRUCT and LIST combinations that are needed for the streaming JSON extension.

## Key Findings

### Vector Type Hierarchy

```rust
// Core vector types from duckdb-rs/crates/duckdb/src/core/vector.rs
pub struct FlatVector    // For primitive types (i64, f64, String, etc.)
pub struct ListVector    // For LIST types
pub struct StructVector  // For STRUCT types  
pub struct ArrayVector   // For fixed-size arrays
```

### Critical API Methods

#### ListVector Methods for Complex Children

```rust
impl ListVector {
    // WRONG: Returns FlatVector (for primitive elements only)
    pub fn child(&self, capacity: usize) -> FlatVector
    
    // CORRECT: Returns StructVector for STRUCT elements
    pub fn struct_child(&self, capacity: usize) -> StructVector
    
    // Also available for other complex types:
    pub fn list_child(&self) -> ListVector
    pub fn array_child(&self) -> ArrayVector
}
```

#### StructVector Methods for Field Access

```rust
impl StructVector {
    // Get child vector for field at index
    pub fn child(&self, idx: usize, capacity: usize) -> FlatVector
    
    // Get nested STRUCT child
    pub fn struct_vector_child(&self, idx: usize) -> StructVector
    
    // Get LIST child  
    pub fn list_vector_child(&self, idx: usize) -> ListVector
    
    // Get ARRAY child
    pub fn array_vector_child(&self, idx: usize) -> ArrayVector
    
    // Utility methods
    pub fn num_children(&self) -> usize
    pub fn child_name(&self, idx: usize) -> DuckDbString
    pub fn set_null(&mut self, row: usize)
}
```

### Correct Pattern for LIST[STRUCT] Insertion

Based on `duckdb-rs/crates/duckdb/src/vtab/arrow.rs` lines 628 and 985-988:

```rust
// For LIST[STRUCT] types:
let mut list_vector = output.list_vector(col_idx);

// WRONG (what we were doing):
let mut child_vector = list_vector.child(arr.len()); // Returns FlatVector

// CORRECT (what we should do):
let mut struct_vector = list_vector.struct_child(arr.len()); // Returns StructVector

// Then insert STRUCT data:
for (field_idx, field) in fields.iter().enumerate() {
    let mut field_vector = struct_vector.child(field_idx, 1);
    // Insert field data into field_vector
}
```

