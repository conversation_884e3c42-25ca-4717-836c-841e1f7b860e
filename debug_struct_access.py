#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_struct_access():
    """Test struct field access that was causing crashes"""
    
    # Create test data - same as failing test
    data = {
        "metadata": {
            "name": "test_dataset",
            "version": "1.0",
            "count": 42
        },
        "simple_field": "value"
    }
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        # Connect to DuckDB and load extension
        os.environ['RUST_BACKTRACE'] = '1'
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        print("Testing struct field access...")
        print(f"Data: {data}")
        
        # Test full object access first
        print("\n1. Testing full object access:")
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Full result: {result}")
        
        # Test metadata column access
        print("\n2. Testing metadata column access:")
        result = conn.execute(f'SELECT metadata FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Metadata result: {result}")
        
        # Test STRUCT field access (this was causing the crash)
        print("\n3. Testing STRUCT field access:")
        try:
            result = conn.execute(f'SELECT metadata.name FROM streaming_json_reader("{temp_file}")').fetchall()
            print(f"Struct access result: {result}")
        except Exception as e:
            print(f"Struct access failed: {e}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up
        os.unlink(temp_file)

if __name__ == "__main__":
    test_struct_access()
