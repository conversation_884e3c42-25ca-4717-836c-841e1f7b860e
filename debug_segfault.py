#!/usr/bin/env python3

import duckdb
import json
import tempfile
import os

def test_simple_case():
    """Test the simplest case that causes segfault"""
    
    # Create test data - same as failing test
    data = [
        {"a": 1, "b": "x"},      # First object: a, b order
        {"b": "y", "a": 10}      # Second object: b, a order (different order)
    ]
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(data, f)
        temp_file = f.name
    
    try:
        # Connect to DuckDB and load extension
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        conn.execute("LOAD './build/debug/streaming_json_reader.duckdb_extension'")
        
        print("Testing simple mixed field order case...")
        print(f"Data: {data}")
        
        # This should cause the segfault
        result = conn.execute(f'SELECT * FROM streaming_json_reader("{temp_file}")').fetchall()
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up
        os.unlink(temp_file)

if __name__ == "__main__":
    test_simple_case()
